using UnityEngine;

/// <summary>
/// Medkit pickup item for healing players and AI
/// </summary>
public class MedkitPickup : MonoBehaviour
{
    [Header("Medkit Settings")]
    public float healAmount = 50f;
    public bool canHealPlayer = true;
    public bool canHealSquadMate = true;
    public AudioClip pickupSound;
    
    [Header("Visual Settings")]
    public float rotationSpeed = 50f;
    public float bobSpeed = 2f;
    public float bobHeight = 0.5f;
    
    private Vector3 startPosition;
    private AudioSource audioSource;
    private Renderer medkitRenderer;
    private bool isPickedUp = false;

    void Start()
    {
        startPosition = transform.position;
        audioSource = GetComponent<AudioSource>();
        medkitRenderer = GetComponent<Renderer>();
        
        // Set green color for medkit
        if (medkitRenderer != null)
        {
            medkitRenderer.material.color = Color.green;
        }
        
        // Ensure we have a trigger collider
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.isTrigger = true;
        }
    }

    void Update()
    {
        if (isPickedUp) return;
        
        // Rotate the medkit
        transform.Rotate(Vector3.up * rotationSpeed * Time.deltaTime);
        
        // Bob up and down
        float newY = startPosition.y + Mathf.Sin(Time.time * bobSpeed) * bobHeight;
        transform.position = new Vector3(transform.position.x, newY, transform.position.z);
    }

    void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;
        
        bool wasPickedUp = false;
        
        // Check if player picked it up
        if (canHealPlayer && other.CompareTag("Player"))
        {
            PlayerController playerController = other.GetComponent<PlayerController>();
            if (playerController != null && playerController.currentHealth < playerController.maxHealth)
            {
                playerController.Heal(healAmount);
                wasPickedUp = true;
                Debug.Log($"💊 Player picked up medkit and healed {healAmount} HP");
            }
        }
        
        // Check if squadmate picked it up
        if (canHealSquadMate && other.CompareTag("SquadMate"))
        {
            SquadMateAgent squadMate = other.GetComponent<SquadMateAgent>();
            if (squadMate != null && squadMate.currentHealth < squadMate.maxHealth)
            {
                squadMate.Heal(healAmount);
                wasPickedUp = true;
                Debug.Log($"💊 SquadMate picked up medkit and healed {healAmount} HP");
                
                // Give reward for picking up medkit when injured
                if (squadMate.currentHealth < squadMate.maxHealth * 0.8f)
                {
                    squadMate.AddReward(0.3f); // Good decision to heal when injured
                }
                else
                {
                    squadMate.AddReward(0.1f); // Small reward for collecting resources
                }
            }
        }
        
        if (wasPickedUp)
        {
            PickupMedkit();
        }
    }

    void PickupMedkit()
    {
        isPickedUp = true;
        
        // Play pickup sound
        if (audioSource != null && pickupSound != null)
        {
            audioSource.PlayOneShot(pickupSound);
        }
        
        // Remove from environment
        GameEnvironment environment = FindObjectOfType<GameEnvironment>();
        if (environment != null)
        {
            environment.RemoveMedkit(gameObject);
        }
        
        // Hide the visual
        if (medkitRenderer != null)
        {
            medkitRenderer.enabled = false;
        }
        
        // Disable collider
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.enabled = false;
        }
        
        // Destroy after sound finishes
        Destroy(gameObject, 1f);
    }

    void OnDrawGizmosSelected()
    {
        // Draw pickup range
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position, 1f);
    }
}
