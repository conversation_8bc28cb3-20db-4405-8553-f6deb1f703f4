using UnityEngine;

/// <summary>
/// Weapon pickup item for arming players and AI
/// </summary>
public class WeaponPickup : MonoBehaviour
{
    [Header("Weapon Settings")]
    public string weaponName = "Rifle";
    public float damage = 35f;
    public float range = 15f;
    public float fireRate = 0.3f;
    public int ammoCount = 30;
    public WeaponType weaponType = WeaponType.AssaultRifle;
    
    [Header("Visual Settings")]
    public float rotationSpeed = 30f;
    public float bobSpeed = 1.5f;
    public float bobHeight = 0.3f;
    
    private Vector3 startPosition;
    private AudioSource audioSource;
    private Renderer weaponRenderer;
    private bool isPickedUp = false;

    public enum WeaponType
    {
        AssaultRifle,
        SMG,
        Shotgun,
        Sniper,
        Pistol
    }

    void Start()
    {
        startPosition = transform.position;
        audioSource = GetComponent<AudioSource>();
        weaponRenderer = GetComponent<Renderer>();
        
        // Set color based on weapon type
        if (weaponRenderer != null)
        {
            switch (weaponType)
            {
                case WeaponType.AssaultRifle:
                    weaponRenderer.material.color = Color.blue;
                    break;
                case WeaponType.SMG:
                    weaponRenderer.material.color = Color.cyan;
                    break;
                case WeaponType.Shotgun:
                    weaponRenderer.material.color = Color.yellow;
                    break;
                case WeaponType.Sniper:
                    weaponRenderer.material.color = Color.magenta;
                    break;
                case WeaponType.Pistol:
                    weaponRenderer.material.color = Color.gray;
                    break;
            }
        }
        
        // Ensure we have a trigger collider
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.isTrigger = true;
        }
    }

    void Update()
    {
        if (isPickedUp) return;
        
        // Rotate the weapon
        transform.Rotate(Vector3.up * rotationSpeed * Time.deltaTime);
        
        // Bob up and down
        float newY = startPosition.y + Mathf.Sin(Time.time * bobSpeed) * bobHeight;
        transform.position = new Vector3(transform.position.x, newY, transform.position.z);
    }

    void OnTriggerEnter(Collider other)
    {
        if (isPickedUp) return;
        
        bool wasPickedUp = false;
        
        // Check if player picked it up
        if (other.CompareTag("Player"))
        {
            PlayerController playerController = other.GetComponent<PlayerController>();
            if (playerController != null)
            {
                // Give weapon to player (simplified - just log for now)
                wasPickedUp = true;
                Debug.Log($"🔫 Player picked up {weaponName} (Damage: {damage}, Range: {range})");
            }
        }
        
        // Check if squadmate picked it up
        if (other.CompareTag("SquadMate"))
        {
            SquadMateAgent squadMate = other.GetComponent<SquadMateAgent>();
            if (squadMate != null)
            {
                // Give weapon to squadmate
                squadMate.EquipWeapon(weaponName, damage, range, fireRate, ammoCount);
                wasPickedUp = true;
                Debug.Log($"🔫 SquadMate picked up {weaponName} (Damage: {damage}, Range: {range})");
                
                // Give reward for picking up better weapons
                float weaponValue = GetWeaponValue();
                squadMate.AddReward(weaponValue * 0.2f);
            }
        }
        
        if (wasPickedUp)
        {
            PickupWeapon();
        }
    }

    float GetWeaponValue()
    {
        // Return weapon priority value (higher = better)
        switch (weaponType)
        {
            case WeaponType.AssaultRifle: return 1.0f; // Best overall
            case WeaponType.SMG: return 0.7f;
            case WeaponType.Shotgun: return 0.8f;
            case WeaponType.Sniper: return 0.9f;
            case WeaponType.Pistol: return 0.3f; // Worst
            default: return 0.5f;
        }
    }

    void PickupWeapon()
    {
        isPickedUp = true;
        
        // Remove from environment
        GameEnvironment environment = FindObjectOfType<GameEnvironment>();
        if (environment != null)
        {
            environment.RemoveWeapon(gameObject);
        }
        
        // Hide the visual
        if (weaponRenderer != null)
        {
            weaponRenderer.enabled = false;
        }
        
        // Disable collider
        Collider col = GetComponent<Collider>();
        if (col != null)
        {
            col.enabled = false;
        }
        
        // Destroy after a short delay
        Destroy(gameObject, 0.5f);
    }

    void OnDrawGizmosSelected()
    {
        // Draw pickup range
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(transform.position, 1f);
        
        // Draw weapon range
        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(transform.position, range);
    }
}
