using UnityEngine;
using UnityEditor;

/// <summary>
/// Editor script to create PUBG-style loot prefabs
/// </summary>
public class CreateLootPrefabs : EditorWindow
{
    [MenuItem("SquadMate/Create Loot Prefabs")]
    static void ShowWindow()
    {
        GetWindow<CreateLootPrefabs>("Create Loot Prefabs");
    }

    void OnGUI()
    {
        GUILayout.Label("🎒 PUBG Loot Prefab Creator", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("Weapons:", EditorStyles.boldLabel);
        if (GUILayout.Button("Create M416 Prefab"))
        {
            CreateM416Prefab();
        }
        if (GUILayout.Button("Create UMP45 Prefab"))
        {
            CreateUMP45Prefab();
        }
        if (GUILayout.Button("Create Kar98k Prefab"))
        {
            CreateKar98kPrefab();
        }

        GUILayout.Space(10);
        GUILayout.Label("Healing:", EditorStyles.boldLabel);
        if (GUILayout.Button("Create MedKit Prefab"))
        {
            CreateMedKitPrefab();
        }
        if (GUILayout.Button("Create FirstAid Prefab"))
        {
            CreateFirstAidPrefab();
        }
        if (GUILayout.Button("Create EnergyDrink Prefab"))
        {
            CreateEnergyDrinkPrefab();
        }

        GUILayout.Space(10);
        GUILayout.Label("Utilities:", EditorStyles.boldLabel);
        if (GUILayout.Button("Create Smoke Grenade Prefab"))
        {
            CreateSmokeGrenadePrefab();
        }
        if (GUILayout.Button("Create Frag Grenade Prefab"))
        {
            CreateFragGrenadePrefab();
        }

        GUILayout.Space(20);
        if (GUILayout.Button("🚀 CREATE ALL LOOT PREFABS", GUILayout.Height(40)))
        {
            CreateAllLootPrefabs();
        }
    }

    static void CreateM416Prefab()
    {
        GameObject weapon = CreateBaseLootObject("M416", PrimitiveType.Cylinder);
        
        // Add WeaponLoot component
        WeaponLoot weaponLoot = weapon.AddComponent<WeaponLoot>();
        WeaponLoot.CreateM416(weapon);
        
        // Set visual properties
        weapon.transform.localScale = new Vector3(0.3f, 1.2f, 0.3f);
        weapon.transform.rotation = Quaternion.Euler(0, 0, 90);
        
        SavePrefab(weapon, "M416");
    }

    static void CreateUMP45Prefab()
    {
        GameObject weapon = CreateBaseLootObject("UMP45", PrimitiveType.Cylinder);
        
        WeaponLoot weaponLoot = weapon.AddComponent<WeaponLoot>();
        WeaponLoot.CreateUMP45(weapon);
        
        weapon.transform.localScale = new Vector3(0.25f, 0.8f, 0.25f);
        weapon.transform.rotation = Quaternion.Euler(0, 0, 90);
        
        SavePrefab(weapon, "UMP45");
    }

    static void CreateKar98kPrefab()
    {
        GameObject weapon = CreateBaseLootObject("Kar98k", PrimitiveType.Cylinder);
        
        WeaponLoot weaponLoot = weapon.AddComponent<WeaponLoot>();
        WeaponLoot.CreateKar98k(weapon);
        
        weapon.transform.localScale = new Vector3(0.2f, 1.5f, 0.2f);
        weapon.transform.rotation = Quaternion.Euler(0, 0, 90);
        
        SavePrefab(weapon, "Kar98k");
    }

    static void CreateMedKitPrefab()
    {
        GameObject healing = CreateBaseLootObject("MedKit", PrimitiveType.Cube);
        
        HealingLoot healingLoot = healing.AddComponent<HealingLoot>();
        HealingLoot.CreateMedKit(healing);
        
        healing.transform.localScale = new Vector3(0.6f, 0.3f, 0.8f);
        
        SavePrefab(healing, "MedKit");
    }

    static void CreateFirstAidPrefab()
    {
        GameObject healing = CreateBaseLootObject("FirstAidKit", PrimitiveType.Cube);
        
        HealingLoot healingLoot = healing.AddComponent<HealingLoot>();
        HealingLoot.CreateFirstAidKit(healing);
        
        healing.transform.localScale = new Vector3(0.5f, 0.2f, 0.7f);
        
        SavePrefab(healing, "FirstAidKit");
    }

    static void CreateEnergyDrinkPrefab()
    {
        GameObject healing = CreateBaseLootObject("EnergyDrink", PrimitiveType.Cylinder);
        
        HealingLoot healingLoot = healing.AddComponent<HealingLoot>();
        HealingLoot.CreateEnergyDrink(healing);
        
        healing.transform.localScale = new Vector3(0.3f, 0.6f, 0.3f);
        
        SavePrefab(healing, "EnergyDrink");
    }

    static void CreateSmokeGrenadePrefab()
    {
        GameObject utility = CreateBaseLootObject("SmokeGrenade", PrimitiveType.Sphere);
        
        // Add basic LootItem component (create UtilityLoot later if needed)
        LootItem lootItem = utility.AddComponent<LootItem>();
        
        utility.transform.localScale = Vector3.one * 0.4f;
        
        // Set gray color for smoke
        Renderer renderer = utility.GetComponent<Renderer>();
        renderer.material.color = Color.gray;
        
        SavePrefab(utility, "SmokeGrenade");
    }

    static void CreateFragGrenadePrefab()
    {
        GameObject utility = CreateBaseLootObject("FragGrenade", PrimitiveType.Sphere);
        
        LootItem lootItem = utility.AddComponent<LootItem>();
        
        utility.transform.localScale = Vector3.one * 0.35f;
        
        // Set dark green color for frag
        Renderer renderer = utility.GetComponent<Renderer>();
        renderer.material.color = new Color(0.2f, 0.4f, 0.2f);
        
        SavePrefab(utility, "FragGrenade");
    }

    static GameObject CreateBaseLootObject(string name, PrimitiveType primitiveType)
    {
        GameObject obj = GameObject.CreatePrimitive(primitiveType);
        obj.name = name;
        obj.tag = "LootItem";
        
        // Make collider a trigger
        Collider collider = obj.GetComponent<Collider>();
        if (collider != null)
        {
            collider.isTrigger = true;
        }
        
        // Create material
        Renderer renderer = obj.GetComponent<Renderer>();
        Material material = new Material(Shader.Find("Standard"));
        renderer.sharedMaterial = material;
        
        return obj;
    }

    static void SavePrefab(GameObject obj, string prefabName)
    {
        // Ensure Prefabs/Loot directory exists
        string lootPath = "Assets/Prefabs/Loot";
        if (!AssetDatabase.IsValidFolder(lootPath))
        {
            if (!AssetDatabase.IsValidFolder("Assets/Prefabs"))
            {
                AssetDatabase.CreateFolder("Assets", "Prefabs");
            }
            AssetDatabase.CreateFolder("Assets/Prefabs", "Loot");
        }
        
        // Save prefab
        string prefabPath = $"{lootPath}/{prefabName}.prefab";
        PrefabUtility.SaveAsPrefabAsset(obj, prefabPath);
        
        // Clean up scene
        DestroyImmediate(obj);
        
        Debug.Log($"✅ Created {prefabName} prefab at {prefabPath}");
    }

    static void CreateAllLootPrefabs()
    {
        Debug.Log("🚀 Creating all PUBG loot prefabs...");
        
        // Weapons
        CreateM416Prefab();
        CreateUMP45Prefab();
        CreateKar98kPrefab();
        
        // Healing
        CreateMedKitPrefab();
        CreateFirstAidPrefab();
        CreateEnergyDrinkPrefab();
        
        // Utilities
        CreateSmokeGrenadePrefab();
        CreateFragGrenadePrefab();
        
        Debug.Log("🎉 All PUBG loot prefabs created successfully!");
        Debug.Log("📁 Check Assets/Prefabs/Loot/ for your new prefabs");
        
        // Refresh asset database
        AssetDatabase.Refresh();
    }
}
